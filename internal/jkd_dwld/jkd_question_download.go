package jkd_dwld

import (
	"QuestionsDownloader/pkg/utils"
	"QuestionsDownloader/types"
	"fmt"
	"log"
)

const (
	exportSubjectConfigsWithUpdateNameFilename = "subject_configs_with_update_name.csv"
)

func QuestionDownloadAndUpZip(downloadDir string, subjectConfigs []types.SubjectConfig) {
	// 下载所有科目题目
	QuestionDownload(downloadDir, subjectConfigs)

	// 解压所有下载的文件
	utils.UnzipFiles(downloadDir)

	// 删除所有压缩文件
	utils.RemoveZipFiles(downloadDir)

	fmt.Println("所有科目题目已下载，并且解压完成...")
}

func QuestionDownload(downloadDir string, subjectConfigs []types.SubjectConfig) {
	// 创建下载根目录（如果存在，则删除后再创建）
	if err := utils.DelAndCreateDirectory(downloadDir); err != nil {
		log.Printf("创建下载目录失败: %v\n", err)
		return
	}

	// 读取配置文件
	processConfig, err := loadProcessConfig(downloadConfigFilename)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 处理所有科目
	exportSubjects := ProcessSubjects(downloadDir, subjectConfigs)
	if exportSubjects == nil || len(exportSubjects) == 0 {
		return
	}

	fmt.Println("所有科目题目已下载完成...")

	utils.ExportSubjectConfigsToCSV(exportSubjects, exportSubjectConfigsWithUpdateNameFilename)
}
